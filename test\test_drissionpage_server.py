#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试DrissionPage版本的美团签名服务器

测试HTTP API接口的功能，验证签名生成是否正常工作。

使用方法:
1. 先启动服务器: python meituan_signature_server.py
2. 运行测试: python test/test_drissionpage_server.py

作者: AI Assistant
日期: 2025-01-02
"""

import requests
import json
import time
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class MeituanSignatureAPITester:
    """美团签名API测试器"""
    
    def __init__(self, base_url: str = "http://localhost:5000"):
        """
        初始化测试器
        
        Args:
            base_url: API服务器基础URL
        """
        self.base_url = base_url
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json',
            'User-Agent': 'MeituanSignatureAPITester/1.0'
        })
        
    def test_health_check(self) -> bool:
        """测试健康检查接口"""
        print("🔍 测试健康检查接口...")
        try:
            response = self.session.get(f"{self.base_url}/api/signature/health")
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 健康检查通过: {data}")
                return True
            else:
                print(f"❌ 健康检查失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 健康检查异常: {e}")
            return False
    
    def test_init_service(self) -> bool:
        """测试服务初始化"""
        print("🚀 测试服务初始化...")
        try:
            response = self.session.post(f"{self.base_url}/api/signature/init")
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    print(f"✅ 服务初始化成功: {data}")
                    return True
                else:
                    print(f"❌ 服务初始化失败: {data}")
                    return False
            else:
                print(f"❌ 初始化请求失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 初始化异常: {e}")
            return False
    
    def test_get_status(self) -> bool:
        """测试获取状态"""
        print("📊 测试获取状态...")
        try:
            response = self.session.get(f"{self.base_url}/api/signature/status")
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 状态获取成功: {data}")
                return data.get('initialized', False)
            else:
                print(f"❌ 状态获取失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 状态获取异常: {e}")
            return False
    
    def test_single_signature(self) -> bool:
        """测试单个签名生成"""
        print("🔐 测试单个签名生成...")
        try:
            test_data = {
                "url": "/s/gateway/login/h5/login/sendLoginFreeSmsCode",
                "method": "POST",
                "data": {
                    "mobile": "17139144117",
                    "countrycode": "86"
                }
            }
            
            response = self.session.post(
                f"{self.base_url}/api/signature/sign",
                json=test_data
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    signature = data.get('signature', {})
                    mtgsig = signature.get('mtgsig')
                    if mtgsig:
                        print(f"✅ 签名生成成功")
                        print(f"   URL: {signature.get('url')}")
                        print(f"   Method: {signature.get('method')}")
                        print(f"   mtgsig: {mtgsig[:50]}...")
                        return True
                    else:
                        print(f"❌ 签名为空: {data}")
                        return False
                else:
                    print(f"❌ 签名生成失败: {data}")
                    return False
            else:
                print(f"❌ 签名请求失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 签名生成异常: {e}")
            return False
    
    def test_batch_signature(self) -> bool:
        """测试批量签名生成"""
        print("📦 测试批量签名生成...")
        try:
            test_data = {
                "requests": [
                    {
                        "url": "/api/user/profile",
                        "method": "GET",
                        "data": None
                    },
                    {
                        "url": "/s/gateway/login/h5/login/loginBySmsCode",
                        "method": "POST",
                        "data": {
                            "mobile": "17139144117",
                            "smsCode": "123456",
                            "countrycode": "86"
                        }
                    }
                ]
            }
            
            response = self.session.post(
                f"{self.base_url}/api/signature/batch",
                json=test_data
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    results = data.get('results', [])
                    success_count = sum(1 for r in results if r.get('success'))
                    print(f"✅ 批量签名生成成功: {success_count}/{len(results)}")
                    return success_count > 0
                else:
                    print(f"❌ 批量签名生成失败: {data}")
                    return False
            else:
                print(f"❌ 批量签名请求失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 批量签名生成异常: {e}")
            return False
    
    def run_all_tests(self) -> bool:
        """运行所有测试"""
        print("🧪 开始运行美团签名API测试...")
        print("=" * 50)
        
        tests = [
            ("健康检查", self.test_health_check),
            ("服务初始化", self.test_init_service),
            ("获取状态", self.test_get_status),
            ("单个签名", self.test_single_signature),
            ("批量签名", self.test_batch_signature)
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            print(f"\n📋 {test_name}测试:")
            try:
                if test_func():
                    passed += 1
                    print(f"✅ {test_name}测试通过")
                else:
                    print(f"❌ {test_name}测试失败")
            except Exception as e:
                print(f"❌ {test_name}测试异常: {e}")
            
            # 测试间隔
            time.sleep(1)
        
        print("\n" + "=" * 50)
        print(f"🎯 测试完成: {passed}/{total} 通过")
        
        if passed == total:
            print("🎉 所有测试通过！DrissionPage版本服务器工作正常")
            return True
        else:
            print("⚠️  部分测试失败，请检查服务器状态")
            return False


def main():
    """主函数"""
    print("美团签名API DrissionPage版本测试")
    print("=" * 50)
    
    # 检查服务器是否运行
    tester = MeituanSignatureAPITester()
    
    print("🔍 检查服务器连接...")
    try:
        response = requests.get("http://localhost:5000/api/signature/health", timeout=5)
        if response.status_code == 200:
            print("✅ 服务器连接正常")
        else:
            print("❌ 服务器响应异常")
            return
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到服务器")
        print("💡 请先启动服务器: python meituan_signature_server.py")
        return
    except Exception as e:
        print(f"❌ 连接检查失败: {e}")
        return
    
    # 运行测试
    success = tester.run_all_tests()
    
    if success:
        print("\n🎊 恭喜！DrissionPage版本的美团签名服务器测试全部通过！")
    else:
        print("\n🔧 请检查服务器配置和DrissionPage环境")


if __name__ == "__main__":
    main()
