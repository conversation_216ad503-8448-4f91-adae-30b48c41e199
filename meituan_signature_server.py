#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
美团签名RPC服务HTTP服务器

通过HTTP API提供美团H5guard签名生成服务，
使用DrissionPage在后台维护美团页面环境。

API接口:
- POST /api/signature/init - 初始化签名服务
- POST /api/signature/sign - 生成单个签名
- POST /api/signature/batch - 批量生成签名
- GET /api/signature/status - 获取服务状态
- GET /api/signature/health - 健康检查

使用方法:
1. 安装依赖: pip install flask DrissionPage
2. 启动服务: python meituan_signature_server.py
3. 访问 http://localhost:5000 查看API文档

作者: AI Assistant
日期: 2025-01-02
"""

import json
import time
import logging
import threading
from typing import Dict, Any, Optional, List
from flask import Flask, request, jsonify, render_template_string
from DrissionPage import ChromiumPage, ChromiumOptions
from DrissionPage.errors import ElementNotFoundError, PageDisconnectedError


class MeituanSignatureService:
    """美团签名服务类"""
    
    def __init__(self, headless: bool = True):
        """
        初始化签名服务
        
        Args:
            headless: 是否使用无头模式
        """
        self.driver = None
        self.initialized = False
        self.headless = headless
        self.lock = threading.Lock()
        self.setup_logging()
        
    def setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)
        
    def setup_driver(self) -> bool:
        """设置DrissionPage浏览器"""
        try:
            with self.lock:
                if self.driver:
                    return True

                # 配置ChromiumOptions
                options = ChromiumOptions()
                if self.headless:
                    options.headless()
                options.add_argument('--no-sandbox')
                options.add_argument('--disable-dev-shm-usage')
                options.add_argument('--disable-gpu')
                options.add_argument('--window-size=1920,1080')
                options.add_argument('--disable-blink-features=AutomationControlled')

                # 禁用图片加载以提高速度
                options.add_argument('--blink-settings=imagesEnabled=false')

                # 创建ChromiumPage实例
                self.driver = ChromiumPage(addr_or_opts=options)
                self.logger.info("DrissionPage浏览器初始化成功")
                return True

        except Exception as e:
            self.logger.error(f"DrissionPage浏览器初始化失败: {e}")
            return False
            
    def init_service(self) -> Dict[str, Any]:
        """初始化签名服务"""
        try:
            if self.initialized:
                return {"success": True, "message": "服务已初始化"}

            # 设置DrissionPage浏览器
            if not self.setup_driver():
                return {"success": False, "error": "DrissionPage浏览器初始化失败"}

            # 访问美团页面
            self.logger.info("正在访问美团页面...")
            self.driver.get("https://sqt.meituan.com")

            # 等待页面加载
            time.sleep(3)

            # 等待H5guard加载
            self.logger.info("等待H5guard加载...")
            time.sleep(5)

            # 检查H5guard是否可用
            h5guard_check = self.driver.run_js("""
                return {
                    loaded: !!(window.H5guard),
                    hasSign: !!(window.H5guard && window.H5guard.sign),
                    version: window.H5guard ? (window.H5guard.version || 'unknown') : null
                };
            """)

            if not h5guard_check['loaded'] or not h5guard_check['hasSign']:
                return {
                    "success": False,
                    "error": "H5guard未正确加载",
                    "details": h5guard_check
                }

            # 加载签名RPC服务脚本
            self.logger.info("加载签名RPC服务...")
            with open('meituan_signature_rpc.js', 'r', encoding='utf-8') as f:
                rpc_script = f.read()

            self.driver.run_js(rpc_script)

            # 初始化签名服务
            self.driver.run_js("""
                window.initPromise = MeituanSignatureAPI.init({
                    debug: false,
                    timeout: 15000
                });
            """)

            # 等待初始化完成
            time.sleep(3)

            # 检查初始化结果
            init_result = self.driver.run_js("""
                return window.initPromise ? { success: true, error: null } : { success: false, error: 'Init failed' };
            """)

            if init_result['success']:
                self.initialized = True
                self.logger.info("签名服务初始化成功")
                return {
                    "success": True,
                    "message": "签名服务初始化成功",
                    "h5guard_version": h5guard_check['version']
                }
            else:
                return {
                    "success": False,
                    "error": f"签名服务初始化失败: {init_result['error']}"
                }

        except Exception as e:
            self.logger.error(f"初始化服务失败: {e}")
            return {"success": False, "error": str(e)}
            
    def generate_signature(self, url: str, method: str, data: Any = None) -> Dict[str, Any]:
        """生成签名"""
        try:
            if not self.initialized:
                return {"success": False, "error": "服务未初始化"}

            with self.lock:
                # 设置参数并调用签名
                self.driver.run_js("""
                    window.signParams = arguments[0];
                    window.signPromise = MeituanSignatureAPI.sign(window.signParams);
                """, {
                    'url': url,
                    'method': method,
                    'data': data
                })

                # 等待签名完成
                time.sleep(2)

                # 获取结果
                result = self.driver.run_js("""
                    return window.signResult || { success: false, error: 'Signature timeout' };
                """)

                # 清理
                self.driver.run_js("delete window.signParams; delete window.signPromise; delete window.signResult;")

            return result

        except Exception as e:
            self.logger.error(f"生成签名失败: {e}")
            return {"success": False, "error": str(e)}

    def batch_generate_signature(self, requests: List[Dict]) -> Dict[str, Any]:
        """批量生成签名"""
        try:
            if not self.initialized:
                return {"success": False, "error": "服务未初始化"}

            with self.lock:
                results = self.driver.run_js("""
                    const requests = arguments[0];
                    return MeituanSignatureAPI.batchSign(requests).then(results => {
                        return { success: true, results: results };
                    }).catch(error => {
                        return { success: false, error: error.message };
                    });
                """, requests)

            return results

        except Exception as e:
            self.logger.error(f"批量生成签名失败: {e}")
            return {"success": False, "error": str(e)}

    def get_status(self) -> Dict[str, Any]:
        """获取服务状态"""
        try:
            if not self.initialized:
                return {
                    "initialized": False,
                    "driver_active": bool(self.driver),
                    "error": "服务未初始化"
                }

            with self.lock:
                status = self.driver.run_js("""
                    return MeituanSignatureAPI.getStatus();
                """)

            return {
                "initialized": self.initialized,
                "driver_active": bool(self.driver),
                "service_status": status
            }

        except Exception as e:
            return {
                "initialized": self.initialized,
                "driver_active": bool(self.driver),
                "error": str(e)
            }

    def cleanup(self):
        """清理资源"""
        with self.lock:
            if self.driver:
                self.driver.quit()
                self.driver = None
            self.initialized = False
            self.logger.info("服务已清理")


# 创建Flask应用和服务实例
app = Flask(__name__)
signature_service = MeituanSignatureService(headless=True)

# API文档HTML模板
API_DOCS_TEMPLATE = """
<!DOCTYPE html>
<html>
<head>
    <title>美团签名API服务</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .endpoint { background: #f5f5f5; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .method { color: white; padding: 3px 8px; border-radius: 3px; font-weight: bold; }
        .post { background: #28a745; }
        .get { background: #007bff; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>美团签名API服务</h1>
    <p>基于美团H5guard签名机制的HTTP API服务</p>
    
    <div class="endpoint">
        <h3><span class="method post">POST</span> /api/signature/init</h3>
        <p>初始化签名服务</p>
        <pre>curl -X POST http://localhost:5000/api/signature/init</pre>
    </div>
    
    <div class="endpoint">
        <h3><span class="method post">POST</span> /api/signature/sign</h3>
        <p>生成单个签名</p>
        <pre>curl -X POST http://localhost:5000/api/signature/sign \\
  -H "Content-Type: application/json" \\
  -d '{
    "url": "/s/gateway/login/h5/login/sendLoginFreeSmsCode",
    "method": "POST",
    "data": {"mobile": "17139144117", "countrycode": "86"}
  }'</pre>
    </div>
    
    <div class="endpoint">
        <h3><span class="method post">POST</span> /api/signature/batch</h3>
        <p>批量生成签名</p>
        <pre>curl -X POST http://localhost:5000/api/signature/batch \\
  -H "Content-Type: application/json" \\
  -d '{
    "requests": [
      {"url": "/api/test1", "method": "GET", "data": null},
      {"url": "/api/test2", "method": "POST", "data": {"key": "value"}}
    ]
  }'</pre>
    </div>
    
    <div class="endpoint">
        <h3><span class="method get">GET</span> /api/signature/status</h3>
        <p>获取服务状态</p>
        <pre>curl http://localhost:5000/api/signature/status</pre>
    </div>
    
    <div class="endpoint">
        <h3><span class="method get">GET</span> /api/signature/health</h3>
        <p>健康检查</p>
        <pre>curl http://localhost:5000/api/signature/health</pre>
    </div>
</body>
</html>
"""


@app.route('/')
def index():
    """API文档首页"""
    return render_template_string(API_DOCS_TEMPLATE)


@app.route('/api/signature/init', methods=['POST'])
def init_signature_service():
    """初始化签名服务"""
    try:
        result = signature_service.init_service()
        return jsonify(result), 200 if result['success'] else 500
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500


@app.route('/api/signature/sign', methods=['POST'])
def generate_signature():
    """生成单个签名"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({"success": False, "error": "请求数据为空"}), 400
            
        url = data.get('url')
        method = data.get('method', 'GET')
        request_data = data.get('data')
        
        if not url:
            return jsonify({"success": False, "error": "缺少url参数"}), 400
            
        result = signature_service.generate_signature(url, method, request_data)
        return jsonify(result), 200 if result['success'] else 500
        
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500


@app.route('/api/signature/batch', methods=['POST'])
def batch_generate_signature():
    """批量生成签名"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({"success": False, "error": "请求数据为空"}), 400
            
        requests = data.get('requests', [])
        if not requests:
            return jsonify({"success": False, "error": "缺少requests参数"}), 400
            
        result = signature_service.batch_generate_signature(requests)
        return jsonify(result), 200 if result['success'] else 500
        
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500


@app.route('/api/signature/status', methods=['GET'])
def get_signature_status():
    """获取服务状态"""
    try:
        status = signature_service.get_status()
        return jsonify(status), 200
    except Exception as e:
        return jsonify({"error": str(e)}), 500


@app.route('/api/signature/health', methods=['GET'])
def health_check():
    """健康检查"""
    return jsonify({
        "status": "healthy",
        "service": "meituan-signature-api",
        "timestamp": time.time(),
        "initialized": signature_service.initialized
    }), 200


@app.errorhandler(404)
def not_found(e):
    """404错误处理"""
    return jsonify({"error": "接口不存在"}), 404


@app.errorhandler(500)
def internal_error(e):
    """500错误处理"""
    return jsonify({"error": "服务器内部错误"}), 500


def cleanup_on_exit():
    """退出时清理资源"""
    signature_service.cleanup()


if __name__ == '__main__':
    import atexit
    atexit.register(cleanup_on_exit)
    
    print("=" * 50)
    print("美团签名API服务启动中...")
    print("API文档: http://localhost:5000")
    print("健康检查: http://localhost:5000/api/signature/health")
    print("=" * 50)
    
    try:
        app.run(host='0.0.0.0', port=5000, debug=False, threaded=True)
    except KeyboardInterrupt:
        print("\n服务已停止")
    finally:
        cleanup_on_exit()
