#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
美团签名RPC服务HTTP服务器 - 简化版

使用DrissionPage提供美团H5guard签名生成服务的简化版本。

API接口:
- POST /api/signature/init - 初始化签名服务
- POST /api/signature/sign - 生成签名
- GET /api/signature/status - 获取服务状态

使用方法:
1. 安装依赖: pip install flask DrissionPage
2. 启动服务: python meituan_signature_server_simple.py
3. 访问 http://localhost:5000

作者: AI Assistant
日期: 2025-01-02
"""

import json
import time
import logging
import threading
from typing import Dict, Any, Optional
from flask import Flask, request, jsonify
from DrissionPage import ChromiumPage, ChromiumOptions

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

app = Flask(__name__)

class MeituanSignatureService:
    """美团签名服务"""
    
    def __init__(self):
        self.driver = None
        self.initialized = False
        self.lock = threading.Lock()
        self.logger = logging.getLogger(__name__)
        
    def setup_browser(self) -> bool:
        """设置浏览器"""
        try:
            if self.driver:
                return True
                
            # 配置浏览器选项
            options = ChromiumOptions()
            options.headless(False)  # 显示浏览器窗口以便调试
            options.set_argument('--no-sandbox')
            options.set_argument('--disable-dev-shm-usage')
            
            # 创建浏览器实例
            self.driver = ChromiumPage(addr_or_opts=options)
            self.logger.info("浏览器初始化成功")
            return True
            
        except Exception as e:
            self.logger.error(f"浏览器初始化失败: {e}")
            return False
    
    def init_service(self) -> Dict[str, Any]:
        """初始化签名服务"""
        try:
            if self.initialized:
                return {"success": True, "message": "服务已初始化"}
                
            # 设置浏览器
            if not self.setup_browser():
                return {"success": False, "error": "浏览器初始化失败"}
                
            # 访问美团页面
            self.logger.info("正在访问美团页面...")
            self.driver.get("https://sqt.meituan.com")
            time.sleep(5)  # 等待页面加载
            
            # 检查H5guard
            h5guard_check = self.driver.run_js("""
                return {
                    loaded: !!(window.H5guard),
                    hasSign: !!(window.H5guard && window.H5guard.sign)
                };
            """)
            
            if not h5guard_check.get('loaded') or not h5guard_check.get('hasSign'):
                return {"success": False, "error": "H5guard未正确加载"}
                
            # 加载RPC脚本
            self.logger.info("加载签名RPC服务...")
            try:
                with open('meituan_signature_rpc.js', 'r', encoding='utf-8') as f:
                    rpc_script = f.read()
                self.driver.run_js(rpc_script)
            except FileNotFoundError:
                return {"success": False, "error": "RPC脚本文件未找到"}
            
            # 初始化签名API
            self.driver.run_js("""
                window.initSignatureAPI = async function() {
                    try {
                        const success = await MeituanSignatureAPI.init({ debug: true });
                        window.signatureAPIReady = success;
                        return success;
                    } catch (error) {
                        window.signatureAPIError = error.message;
                        return false;
                    }
                };
                window.initSignatureAPI();
            """)
            
            # 等待初始化完成
            time.sleep(3)
            
            # 检查初始化结果
            api_ready = self.driver.run_js("return window.signatureAPIReady;")
            if api_ready:
                self.initialized = True
                self.logger.info("签名服务初始化成功")
                return {"success": True, "message": "签名服务初始化成功"}
            else:
                error = self.driver.run_js("return window.signatureAPIError || 'Unknown error';")
                return {"success": False, "error": f"签名API初始化失败: {error}"}
                
        except Exception as e:
            self.logger.error(f"初始化服务失败: {e}")
            return {"success": False, "error": str(e)}
    
    def generate_signature(self, url: str, method: str, data: Any = None) -> Dict[str, Any]:
        """生成签名"""
        try:
            if not self.initialized:
                return {"success": False, "error": "服务未初始化"}
                
            with self.lock:
                # 调用签名生成
                self.driver.run_js("""
                    window.generateSignature = async function(params) {
                        try {
                            const result = await MeituanSignatureAPI.sign(params);
                            window.signatureResult = result;
                            return result;
                        } catch (error) {
                            window.signatureResult = { success: false, error: error.message };
                            return window.signatureResult;
                        }
                    };
                    
                    const params = arguments[0];
                    window.generateSignature(params);
                """, {
                    'url': url,
                    'method': method,
                    'data': data
                })
                
                # 等待结果
                time.sleep(2)
                
                # 获取结果
                result = self.driver.run_js("return window.signatureResult;")
                
                if result:
                    return result
                else:
                    return {"success": False, "error": "签名生成超时"}
                
        except Exception as e:
            self.logger.error(f"生成签名失败: {e}")
            return {"success": False, "error": str(e)}
    
    def get_status(self) -> Dict[str, Any]:
        """获取服务状态"""
        try:
            if not self.initialized:
                return {"initialized": False, "error": "服务未初始化"}
                
            status = self.driver.run_js("return MeituanSignatureAPI.getStatus();")
            return {
                "initialized": self.initialized,
                "browser_active": bool(self.driver),
                "service_status": status
            }
            
        except Exception as e:
            return {"initialized": self.initialized, "error": str(e)}
    
    def cleanup(self):
        """清理资源"""
        if self.driver:
            self.driver.quit()
            self.driver = None
        self.initialized = False


# 创建服务实例
signature_service = MeituanSignatureService()

@app.route('/')
def index():
    """首页"""
    return jsonify({
        "service": "美团签名API服务 - DrissionPage版",
        "version": "1.0.0",
        "endpoints": {
            "init": "POST /api/signature/init",
            "sign": "POST /api/signature/sign",
            "status": "GET /api/signature/status"
        }
    })

@app.route('/api/signature/init', methods=['POST'])
def init_signature_service():
    """初始化签名服务"""
    result = signature_service.init_service()
    return jsonify(result)

@app.route('/api/signature/sign', methods=['POST'])
def generate_signature():
    """生成签名"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({"success": False, "error": "请求数据为空"}), 400
            
        url = data.get('url')
        method = data.get('method')
        request_data = data.get('data')
        
        if not url or not method:
            return jsonify({"success": False, "error": "缺少必要参数"}), 400
            
        result = signature_service.generate_signature(url, method, request_data)
        return jsonify(result)
        
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/signature/status', methods=['GET'])
def get_signature_status():
    """获取签名服务状态"""
    result = signature_service.get_status()
    return jsonify(result)

@app.errorhandler(404)
def not_found(e):
    """404错误处理"""
    return jsonify({"error": "接口不存在"}), 404

@app.errorhandler(500)
def internal_error(e):
    """500错误处理"""
    return jsonify({"error": "服务器内部错误"}), 500

def cleanup_on_exit():
    """退出时清理资源"""
    signature_service.cleanup()

if __name__ == '__main__':
    import atexit
    atexit.register(cleanup_on_exit)
    
    print("=" * 50)
    print("美团签名API服务启动中... (DrissionPage版)")
    print("API文档: http://localhost:5000")
    print("=" * 50)
    
    try:
        app.run(host='0.0.0.0', port=5000, debug=False, threaded=True)
    except KeyboardInterrupt:
        print("\n服务已停止")
    finally:
        cleanup_on_exit()
