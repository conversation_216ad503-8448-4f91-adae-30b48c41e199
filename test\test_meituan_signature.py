#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
美团签名RPC服务测试脚本

此脚本通过Selenium WebDriver调用美团签名RPC服务，
测试签名生成功能是否正常工作。

使用方法:
1. 安装依赖: pip install selenium
2. 下载ChromeDriver并配置PATH
3. 运行测试: python test_meituan_signature.py

作者: AI Assistant
日期: 2025-01-02
"""

import json
import time
import logging
from typing import Dict, Any, Optional, List
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, WebDriverException


class MeituanSignatureTester:
    """美团签名服务测试类"""
    
    def __init__(self, headless: bool = False, debug: bool = True):
        """
        初始化测试器
        
        Args:
            headless: 是否使用无头模式
            debug: 是否开启调试模式
        """
        self.debug = debug
        self.driver = None
        self.setup_logging()
        self.setup_driver(headless)
        
    def setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO if self.debug else logging.WARNING,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(),
                logging.FileHandler('meituan_signature_test.log', encoding='utf-8')
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def setup_driver(self, headless: bool):
        """设置WebDriver"""
        try:
            chrome_options = Options()
            if headless:
                chrome_options.add_argument('--headless')
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1920,1080')
            
            # 禁用图片加载以提高速度
            prefs = {"profile.managed_default_content_settings.images": 2}
            chrome_options.add_experimental_option("prefs", prefs)
            
            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.implicitly_wait(10)
            self.logger.info("WebDriver初始化成功")
            
        except Exception as e:
            self.logger.error(f"WebDriver初始化失败: {e}")
            raise
            
    def load_signature_service(self) -> bool:
        """加载美团签名服务"""
        try:
            # 访问美团页面
            self.logger.info("正在访问美团页面...")
            self.driver.get("https://sqt.meituan.com")
            
            # 等待页面加载
            WebDriverWait(self.driver, 30).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            
            # 等待H5guard加载
            self.logger.info("等待H5guard加载...")
            time.sleep(5)
            
            # 检查H5guard是否可用
            h5guard_check = self.driver.execute_script("""
                return {
                    loaded: !!(window.H5guard),
                    hasSign: !!(window.H5guard && window.H5guard.sign),
                    version: window.H5guard ? (window.H5guard.version || 'unknown') : null
                };
            """)
            
            if not h5guard_check['loaded']:
                self.logger.error("H5guard未加载")
                return False
                
            if not h5guard_check['hasSign']:
                self.logger.error("H5guard.sign方法不可用")
                return False
                
            self.logger.info(f"H5guard加载成功，版本: {h5guard_check['version']}")
            
            # 加载签名RPC服务脚本
            self.logger.info("加载签名RPC服务...")
            with open('../meituan_signature_rpc.js', 'r', encoding='utf-8') as f:
                rpc_script = f.read()
                
            self.driver.execute_script(rpc_script)
            
            # 初始化签名服务
            init_result = self.driver.execute_script("""
                return MeituanSignatureAPI.init({
                    debug: true,
                    timeout: 15000
                }).then(success => {
                    return { success: success, error: null };
                }).catch(error => {
                    return { success: false, error: error.message };
                });
            """)
            
            if init_result['success']:
                self.logger.info("签名服务初始化成功")
                return True
            else:
                self.logger.error(f"签名服务初始化失败: {init_result['error']}")
                return False
                
        except Exception as e:
            self.logger.error(f"加载签名服务失败: {e}")
            return False
            
    def test_single_signature(self, url: str, method: str, data: Any = None) -> Optional[Dict]:
        """测试单个签名生成"""
        try:
            self.logger.info(f"测试签名生成: {method} {url}")
            
            # 调用签名生成
            result = self.driver.execute_script("""
                const params = arguments[0];
                return MeituanSignatureAPI.sign(params).then(result => {
                    return result;
                }).catch(error => {
                    return { success: false, error: error.message };
                });
            """, {
                'url': url,
                'method': method,
                'data': data
            })
            
            if result['success']:
                self.logger.info("签名生成成功")
                self.logger.info(f"签名: {result['signature']['mtgsig'][:50]}...")
                return result
            else:
                self.logger.error(f"签名生成失败: {result['error']}")
                return None
                
        except Exception as e:
            self.logger.error(f"签名测试异常: {e}")
            return None
            
    def test_batch_signatures(self, requests: List[Dict]) -> Optional[List[Dict]]:
        """测试批量签名生成"""
        try:
            self.logger.info(f"测试批量签名生成，共{len(requests)}个请求")
            
            # 调用批量签名生成
            results = self.driver.execute_script("""
                const requests = arguments[0];
                return MeituanSignatureAPI.batchSign(requests).then(results => {
                    return results;
                }).catch(error => {
                    return [{ success: false, error: error.message }];
                });
            """, requests)
            
            success_count = sum(1 for r in results if r.get('success'))
            self.logger.info(f"批量签名完成，成功: {success_count}/{len(results)}")
            
            return results
            
        except Exception as e:
            self.logger.error(f"批量签名测试异常: {e}")
            return None
            
    def get_service_status(self) -> Optional[Dict]:
        """获取服务状态"""
        try:
            status = self.driver.execute_script("""
                return MeituanSignatureAPI.getStatus();
            """)
            
            self.logger.info("服务状态:")
            for key, value in status.items():
                self.logger.info(f"  {key}: {value}")
                
            return status
            
        except Exception as e:
            self.logger.error(f"获取服务状态失败: {e}")
            return None
            
    def run_tests(self):
        """运行所有测试"""
        try:
            self.logger.info("=" * 50)
            self.logger.info("开始美团签名RPC服务测试")
            self.logger.info("=" * 50)
            
            # 1. 加载签名服务
            if not self.load_signature_service():
                self.logger.error("签名服务加载失败，测试终止")
                return False
                
            # 2. 获取服务状态
            self.logger.info("\n--- 服务状态检查 ---")
            self.get_service_status()
            
            # 3. 测试单个签名生成
            self.logger.info("\n--- 单个签名测试 ---")
            
            # 测试发送验证码签名
            sms_result = self.test_single_signature(
                url='/s/gateway/login/h5/login/sendLoginFreeSmsCode',
                method='POST',
                data={
                    'mobile': '17139144117',
                    'countrycode': '86'
                }
            )
            
            # 测试GET请求签名
            get_result = self.test_single_signature(
                url='/api/user/profile',
                method='GET',
                data=None
            )
            
            # 4. 测试批量签名生成
            self.logger.info("\n--- 批量签名测试 ---")
            batch_requests = [
                {
                    'url': '/api/orders/list',
                    'method': 'POST',
                    'data': {'page': 1, 'size': 20}
                },
                {
                    'url': '/api/settings/get',
                    'method': 'GET',
                    'data': None
                },
                {
                    'url': '/api/user/update',
                    'method': 'PUT',
                    'data': {'name': 'test'}
                }
            ]
            
            batch_results = self.test_batch_signatures(batch_requests)
            
            # 5. 测试结果汇总
            self.logger.info("\n--- 测试结果汇总 ---")
            
            tests_passed = 0
            total_tests = 0
            
            if sms_result and sms_result.get('success'):
                tests_passed += 1
            total_tests += 1
            
            if get_result and get_result.get('success'):
                tests_passed += 1
            total_tests += 1
            
            if batch_results:
                batch_success = sum(1 for r in batch_results if r.get('success'))
                tests_passed += batch_success
                total_tests += len(batch_results)
            
            self.logger.info(f"测试通过: {tests_passed}/{total_tests}")
            
            if tests_passed == total_tests:
                self.logger.info("🎉 所有测试通过！签名服务工作正常")
                return True
            else:
                self.logger.warning(f"⚠️  部分测试失败，通过率: {tests_passed/total_tests*100:.1f}%")
                return False
                
        except Exception as e:
            self.logger.error(f"测试运行异常: {e}")
            return False
            
    def cleanup(self):
        """清理资源"""
        if self.driver:
            self.driver.quit()
            self.logger.info("WebDriver已关闭")


def main():
    """主函数"""
    tester = None
    try:
        # 创建测试器
        tester = MeituanSignatureTester(headless=False, debug=True)
        
        # 运行测试
        success = tester.run_tests()
        
        if success:
            print("\n✅ 测试完成，签名服务工作正常")
        else:
            print("\n❌ 测试失败，请检查日志")
            
    except KeyboardInterrupt:
        print("\n用户中断测试")
    except Exception as e:
        print(f"\n测试异常: {e}")
    finally:
        if tester:
            tester.cleanup()


if __name__ == "__main__":
    main()
