#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
美团签名API客户端测试脚本

测试美团签名HTTP API服务的各个接口功能。

使用方法:
1. 启动签名服务器: python meituan_signature_server.py
2. 运行测试: python test_signature_api_client.py

作者: AI Assistant
日期: 2025-01-02
"""

import json
import time
import requests
import logging
from typing import Dict, Any, Optional, List


class MeituanSignatureAPIClient:
    """美团签名API客户端"""
    
    def __init__(self, base_url: str = "http://localhost:5000", timeout: int = 30):
        """
        初始化API客户端
        
        Args:
            base_url: API服务器地址
            timeout: 请求超时时间(秒)
        """
        self.base_url = base_url.rstrip('/')
        self.timeout = timeout
        self.session = requests.Session()
        self.setup_logging()
        
    def setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)
        
    def _make_request(self, method: str, endpoint: str, data: Optional[Dict] = None) -> Dict[str, Any]:
        """发送HTTP请求"""
        url = f"{self.base_url}{endpoint}"
        
        try:
            if method.upper() == 'GET':
                response = self.session.get(url, timeout=self.timeout)
            elif method.upper() == 'POST':
                response = self.session.post(
                    url, 
                    json=data, 
                    headers={'Content-Type': 'application/json'},
                    timeout=self.timeout
                )
            else:
                raise ValueError(f"不支持的HTTP方法: {method}")
                
            return {
                "success": response.status_code == 200,
                "status_code": response.status_code,
                "data": response.json() if response.content else {},
                "error": None if response.status_code == 200 else f"HTTP {response.status_code}"
            }
            
        except requests.exceptions.RequestException as e:
            return {
                "success": False,
                "status_code": None,
                "data": {},
                "error": str(e)
            }
        except Exception as e:
            return {
                "success": False,
                "status_code": None,
                "data": {},
                "error": str(e)
            }
            
    def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        self.logger.info("执行健康检查...")
        return self._make_request('GET', '/api/signature/health')
        
    def init_service(self) -> Dict[str, Any]:
        """初始化签名服务"""
        self.logger.info("初始化签名服务...")
        return self._make_request('POST', '/api/signature/init')
        
    def generate_signature(self, url: str, method: str = 'GET', data: Any = None) -> Dict[str, Any]:
        """生成单个签名"""
        self.logger.info(f"生成签名: {method} {url}")
        
        request_data = {
            'url': url,
            'method': method,
            'data': data
        }
        
        return self._make_request('POST', '/api/signature/sign', request_data)
        
    def batch_generate_signature(self, requests: List[Dict]) -> Dict[str, Any]:
        """批量生成签名"""
        self.logger.info(f"批量生成签名，共{len(requests)}个请求")
        
        request_data = {
            'requests': requests
        }
        
        return self._make_request('POST', '/api/signature/batch', request_data)
        
    def get_status(self) -> Dict[str, Any]:
        """获取服务状态"""
        self.logger.info("获取服务状态...")
        return self._make_request('GET', '/api/signature/status')


def test_api_client():
    """测试API客户端"""
    client = MeituanSignatureAPIClient()
    
    print("=" * 60)
    print("美团签名API客户端测试")
    print("=" * 60)
    
    # 1. 健康检查
    print("\n1. 健康检查")
    print("-" * 30)
    health_result = client.health_check()
    if health_result['success']:
        print("✅ 健康检查通过")
        print(f"   服务状态: {health_result['data'].get('status')}")
        print(f"   初始化状态: {health_result['data'].get('initialized')}")
    else:
        print("❌ 健康检查失败")
        print(f"   错误: {health_result['error']}")
        return False
        
    # 2. 初始化服务
    print("\n2. 初始化签名服务")
    print("-" * 30)
    init_result = client.init_service()
    if init_result['success'] and init_result['data'].get('success'):
        print("✅ 服务初始化成功")
        print(f"   消息: {init_result['data'].get('message')}")
        if 'h5guard_version' in init_result['data']:
            print(f"   H5guard版本: {init_result['data']['h5guard_version']}")
    else:
        print("❌ 服务初始化失败")
        error_msg = init_result['data'].get('error') if init_result['success'] else init_result['error']
        print(f"   错误: {error_msg}")
        return False
        
    # 等待服务完全初始化
    print("   等待服务完全初始化...")
    time.sleep(3)
    
    # 3. 获取服务状态
    print("\n3. 获取服务状态")
    print("-" * 30)
    status_result = client.get_status()
    if status_result['success']:
        print("✅ 状态获取成功")
        status_data = status_result['data']
        print(f"   初始化状态: {status_data.get('initialized')}")
        print(f"   WebDriver状态: {status_data.get('driver_active')}")
        
        service_status = status_data.get('service_status', {})
        if service_status:
            print(f"   H5guard可用: {service_status.get('h5guardAvailable')}")
            print(f"   当前位置: {service_status.get('location', 'N/A')}")
    else:
        print("❌ 状态获取失败")
        print(f"   错误: {status_result['error']}")
        
    # 4. 测试单个签名生成
    print("\n4. 测试单个签名生成")
    print("-" * 30)
    
    # 测试发送验证码签名
    sms_result = client.generate_signature(
        url='/s/gateway/login/h5/login/sendLoginFreeSmsCode',
        method='POST',
        data={
            'mobile': '17139144117',
            'countrycode': '86'
        }
    )
    
    if sms_result['success'] and sms_result['data'].get('success'):
        print("✅ 验证码签名生成成功")
        signature_data = sms_result['data']['signature']
        print(f"   签名: {signature_data['mtgsig'][:50]}...")
        print(f"   URL: {signature_data['url']}")
        print(f"   方法: {signature_data['method']}")
    else:
        print("❌ 验证码签名生成失败")
        error_msg = sms_result['data'].get('error') if sms_result['success'] else sms_result['error']
        print(f"   错误: {error_msg}")
        
    # 测试GET请求签名
    get_result = client.generate_signature(
        url='/api/user/profile',
        method='GET'
    )
    
    if get_result['success'] and get_result['data'].get('success'):
        print("✅ GET请求签名生成成功")
        signature_data = get_result['data']['signature']
        print(f"   签名: {signature_data['mtgsig'][:50]}...")
    else:
        print("❌ GET请求签名生成失败")
        error_msg = get_result['data'].get('error') if get_result['success'] else get_result['error']
        print(f"   错误: {error_msg}")
        
    # 5. 测试批量签名生成
    print("\n5. 测试批量签名生成")
    print("-" * 30)
    
    batch_requests = [
        {
            'url': '/api/orders/list',
            'method': 'POST',
            'data': {'page': 1, 'size': 20}
        },
        {
            'url': '/api/settings/get',
            'method': 'GET',
            'data': None
        },
        {
            'url': '/api/user/update',
            'method': 'PUT',
            'data': {'name': 'test'}
        }
    ]
    
    batch_result = client.batch_generate_signature(batch_requests)
    
    if batch_result['success'] and batch_result['data'].get('success'):
        print("✅ 批量签名生成成功")
        results = batch_result['data']['results']
        success_count = sum(1 for r in results if r.get('success'))
        print(f"   成功数量: {success_count}/{len(results)}")
        
        for i, result in enumerate(results):
            if result.get('success'):
                signature = result['signature']['mtgsig'][:30]
                print(f"   请求{i}: ✅ {signature}...")
            else:
                print(f"   请求{i}: ❌ {result.get('error', 'Unknown error')}")
    else:
        print("❌ 批量签名生成失败")
        error_msg = batch_result['data'].get('error') if batch_result['success'] else batch_result['error']
        print(f"   错误: {error_msg}")
        
    # 6. 测试结果汇总
    print("\n6. 测试结果汇总")
    print("-" * 30)
    
    tests = [
        ("健康检查", health_result['success']),
        ("服务初始化", init_result['success'] and init_result['data'].get('success')),
        ("状态获取", status_result['success']),
        ("验证码签名", sms_result['success'] and sms_result['data'].get('success')),
        ("GET签名", get_result['success'] and get_result['data'].get('success')),
        ("批量签名", batch_result['success'] and batch_result['data'].get('success'))
    ]
    
    passed_tests = sum(1 for _, success in tests if success)
    total_tests = len(tests)
    
    print(f"测试通过: {passed_tests}/{total_tests}")
    
    for test_name, success in tests:
        status = "✅" if success else "❌"
        print(f"   {status} {test_name}")
        
    if passed_tests == total_tests:
        print("\n🎉 所有测试通过！API服务工作正常")
        return True
    else:
        print(f"\n⚠️  部分测试失败，通过率: {passed_tests/total_tests*100:.1f}%")
        return False


def main():
    """主函数"""
    try:
        success = test_api_client()
        
        if success:
            print("\n" + "=" * 60)
            print("测试完成，API服务工作正常")
            print("可以开始使用美团签名API服务了！")
            print("=" * 60)
        else:
            print("\n" + "=" * 60)
            print("测试失败，请检查服务器状态")
            print("确保 meituan_signature_server.py 正在运行")
            print("=" * 60)
            
    except KeyboardInterrupt:
        print("\n用户中断测试")
    except Exception as e:
        print(f"\n测试异常: {e}")


if __name__ == "__main__":
    main()
