# 美团签名RPC服务使用文档

## 概述

`meituan_signature_rpc.js` 提供了一个专门用于生成美团H5guard签名的RPC服务，可供其他程序调用。

## 快速开始

### 1. 加载服务

在美团页面的浏览器控制台中加载脚本：

```javascript
// 方法1: 直接加载脚本文件
const script = document.createElement('script');
script.src = 'path/to/meituan_signature_rpc.js';
document.head.appendChild(script);

// 方法2: 复制粘贴代码到控制台
// 将 meituan_signature_rpc.js 的内容复制粘贴到控制台执行
```

### 2. 初始化服务

```javascript
// 初始化签名服务
await MeituanSignatureAPI.init({
    debug: true,      // 开启调试模式
    timeout: 10000    // 设置超时时间
});

console.log('签名服务初始化完成');
```

### 3. 生成签名

```javascript
// 生成单个签名
const result = await MeituanSignatureAPI.sign({
    url: '/s/gateway/login/h5/login/sendLoginFreeSmsCode',
    method: 'POST',
    data: {
        mobile: '17139144117',
        countrycode: '86'
    }
});

console.log('签名结果:', result);
```

## API 接口

### MeituanSignatureAPI.init(options)

初始化签名服务

**参数:**
- `options` (Object): 配置选项
  - `debug` (boolean): 是否开启调试模式，默认 false
  - `timeout` (number): H5guard加载超时时间(毫秒)，默认 10000

**返回值:**
- `Promise<boolean>`: 初始化是否成功

**示例:**
```javascript
const success = await MeituanSignatureAPI.init({
    debug: true,
    timeout: 15000
});

if (success) {
    console.log('初始化成功');
} else {
    console.log('初始化失败');
}
```

### MeituanSignatureAPI.sign(params)

生成单个API签名

**参数:**
- `params` (Object): 签名参数
  - `url` (string): API地址
  - `method` (string): HTTP方法 (GET/POST/PUT/DELETE/PATCH)
  - `data` (Object|string): 请求数据

**返回值:**
- `Promise<Object>`: 签名结果

**签名结果格式:**
```javascript
{
    success: true,
    timestamp: 1754104236500,
    signature: {
        url: "完整的API地址",
        method: "POST",
        headers: {
            mtgsig: "签名字符串"
        },
        data: "请求数据",
        mtgsig: "签名字符串"
    },
    raw: "原始H5guard.sign返回结果"
}
```

**示例:**
```javascript
const result = await MeituanSignatureAPI.sign({
    url: '/s/gateway/login/h5/login/sendLoginFreeSmsCode',
    method: 'POST',
    data: {
        mobile: '17139144117',
        countrycode: '86'
    }
});

if (result.success) {
    console.log('签名:', result.signature.mtgsig);
} else {
    console.error('签名失败:', result.error);
}
```

### MeituanSignatureAPI.batchSign(requestList)

批量生成签名

**参数:**
- `requestList` (Array<Object>): 请求列表，每个元素包含 url, method, data

**返回值:**
- `Promise<Array<Object>>`: 签名结果列表

**示例:**
```javascript
const requests = [
    {
        url: '/api/endpoint1',
        method: 'GET',
        data: null
    },
    {
        url: '/api/endpoint2',
        method: 'POST',
        data: { key: 'value' }
    }
];

const results = await MeituanSignatureAPI.batchSign(requests);
results.forEach((result, index) => {
    console.log(`请求${index}签名结果:`, result);
});
```

### MeituanSignatureAPI.getStatus()

获取服务状态

**返回值:**
- `Object`: 服务状态信息

**状态信息格式:**
```javascript
{
    initialized: true,
    h5guardAvailable: true,
    timestamp: 1754104236500,
    userAgent: "浏览器用户代理",
    location: "当前页面地址"
}
```

## 使用场景

### 场景1: 发送验证码

```javascript
// 初始化
await MeituanSignatureAPI.init({ debug: true });

// 生成发送验证码的签名
const smsResult = await MeituanSignatureAPI.sign({
    url: '/s/gateway/login/h5/login/sendLoginFreeSmsCode',
    method: 'POST',
    data: {
        mobile: '17139144117',
        countrycode: '86'
    }
});

console.log('验证码签名:', smsResult.signature.mtgsig);
```

### 场景2: 验证码登录

```javascript
// 生成登录验证的签名
const loginResult = await MeituanSignatureAPI.sign({
    url: '/s/gateway/login/h5/login/loginBySmsCode',
    method: 'POST',
    data: {
        mobile: '17139144117',
        smsCode: '123456',
        countrycode: '86'
    }
});

console.log('登录签名:', loginResult.signature.mtgsig);
```

### 场景3: 批量API调用

```javascript
// 批量生成多个API的签名
const apiList = [
    {
        url: '/api/user/profile',
        method: 'GET',
        data: null
    },
    {
        url: '/api/orders/list',
        method: 'POST',
        data: { page: 1, size: 20 }
    },
    {
        url: '/api/settings/update',
        method: 'PUT',
        data: { theme: 'dark' }
    }
];

const signatures = await MeituanSignatureAPI.batchSign(apiList);
signatures.forEach((sig, index) => {
    if (sig.success) {
        console.log(`API ${index} 签名:`, sig.signature.mtgsig);
    } else {
        console.error(`API ${index} 签名失败:`, sig.error);
    }
});
```

## 错误处理

```javascript
try {
    const result = await MeituanSignatureAPI.sign({
        url: '/api/test',
        method: 'POST',
        data: { test: true }
    });
    
    if (result.success) {
        // 使用签名
        console.log('签名:', result.signature.mtgsig);
    } else {
        // 处理签名失败
        console.error('签名失败:', result.error);
    }
} catch (error) {
    console.error('调用异常:', error.message);
}
```

## 注意事项

1. **环境要求**: 必须在美团页面环境中运行，确保H5guard已加载
2. **初始化**: 使用前必须先调用 `init()` 方法初始化服务
3. **异步调用**: 所有API都是异步的，需要使用 `await` 或 `.then()`
4. **错误处理**: 建议使用 try-catch 包装调用，处理可能的异常
5. **调试模式**: 开发时建议开启 debug 模式，便于排查问题

## 集成到其他程序

### 通过WebSocket

```javascript
// 在美团页面中运行WebSocket服务器
const ws = new WebSocket('ws://localhost:8080');

ws.onmessage = async (event) => {
    const request = JSON.parse(event.data);
    
    if (request.action === 'sign') {
        const result = await MeituanSignatureAPI.sign(request.params);
        ws.send(JSON.stringify({
            id: request.id,
            result: result
        }));
    }
};
```

### 通过HTTP接口

```javascript
// 创建简单的HTTP服务器（需要配合后端）
window.signatureServer = {
    async handleRequest(params) {
        return await MeituanSignatureAPI.sign(params);
    }
};

// 外部程序可以通过注入脚本调用
```
