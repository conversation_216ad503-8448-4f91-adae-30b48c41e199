# 美团RPC签名生成服务实验报告

## 实验概述

本实验成功实现了美团H5guard签名机制的RPC调用服务，通过浏览器JavaScript与Python客户端的配合，实现了真实的mtgsig签名生成功能。

## 实验环境

- **浏览器**: Chrome/Edge (支持现代JavaScript)
- **美团页面**: https://sqt.meituan.com/
- **Python版本**: 3.7+
- **测试时间**: 2025-01-02

## 实验结果

### ✅ 成功验证的功能

1. **H5guard加载检测** - 成功检测到H5guard对象及其sign方法
2. **发送验证码签名生成** - 成功生成真实的mtgsig签名
3. **通用API签名生成** - 支持任意API路径的签名生成
4. **RPC服务封装** - 完整的JavaScript RPC服务实现

### 📊 测试数据

#### 发送验证码API签名
```json
{
  "success": true,
  "mtgsig": "{\"a1\":\"1.2\",\"a2\":1754113277514,\"a3\":\"x8uu03x4695u5v7xz704v9386zu4wy9680131x85yx497958yx5wz7yv\",\"a5\":\"g7eK9Fwi2tPoL2fpHkQSkIjFBGrXqAy/nsdalFqJfnZWd14TVvI=\",\"a6\":\"h1.84632+gb3q1ZonXKyFiZyhoOI1s3Pbe/vEyu+oM7h0vg6IJEk78eO5+R54HVW1+eTKK5WznUc844roArnGRKgvathbGkDpA1PQuXNDXWE8UxKtUXJfy5H366mqAlv3pd3CjhTfGu+j2lMqhvDuf+lPw==\",\"a8\":\"fb015e7c6d200b47499f13c4c29bccd2\",\"a9\":\"3.2.1,7,8\",\"a10\":\"8f\",\"x0\":4,\"d1\":\"4e91b5c224c3a7328aabd1216a5212fb\"}",
  "url": "/s/gateway/login/h5/login/sendLoginFreeSmsCode",
  "method": "POST",
  "data": {
    "mobile": "13800138000",
    "countrycode": "86"
  }
}
```

#### 用户信息API签名
```json
{
  "success": true,
  "mtgsig": "{\"a1\":\"1.2\",\"a2\":1754113285188,\"a3\":\"x8uu03x4695u5v7xz704v9386zu4wy9680131x85yx497958yx5wz7yv\",\"a5\":\"8zX2iCoPumFtv8ytWM/dxuFqHaLmgURnFT3b6AdD5KiBw0618jr=\",\"a6\":\"h1.8HWW8j3FQbgTEGaA5dHAp/7vRWEjbgKsPs/cHCZ8VHJfTmSN1/VgYQIqb5uDJ4/HkB3sElOL91eZuQ0N7yY6MIgrYjYiYKJsanF4hfIHlJqvzH4uwrOznedHZXtZYb/BY9bkpbALyLq+ie2XPjeSpDg==\",\"a8\":\"********************************\",\"a9\":\"3.2.1,7,8\",\"a10\":\"8f\",\"x0\":4,\"d1\":\"6d6813b7e7e264495b2aca3f8a8a77f3\"}",
  "url": "/s/gateway/user/h5/userInfo",
  "method": "GET"
}
```

## 核心技术实现

### 1. H5guard检测与等待

```javascript
function waitForH5guard(timeout = 10000) {
    return new Promise((resolve, reject) => {
        if (window.H5guard) {
            resolve(window.H5guard);
            return;
        }

        const startTime = Date.now();
        const checkInterval = setInterval(() => {
            if (window.H5guard) {
                clearInterval(checkInterval);
                resolve(window.H5guard);
            } else if (Date.now() - startTime > timeout) {
                clearInterval(checkInterval);
                reject(new Error('H5guard加载超时'));
            }
        }, 100);
    });
}
```

### 2. RPC服务核心实现

```javascript
class MeituanRealRPC {
    async generateMtgsig(url, method, data = {}) {
        try {
            if (!window.H5guard) {
                throw new Error('H5guard未加载');
            }
            
            const signedRequest = await window.H5guard.sign({
                url: url,
                method: method.toUpperCase(),
                data: data
            });
            
            return {
                success: true,
                mtgsig: signedRequest.headers.mtgsig,
                url: signedRequest.url,
                method: signedRequest.method,
                data: signedRequest.data,
                headers: signedRequest.headers,
                timestamp: Date.now()
            };
        } catch (error) {
            return {
                success: false,
                error: error.message,
                timestamp: Date.now()
            };
        }
    }
}
```

### 3. 便捷调用方法

```javascript
// 快速获取mtgsig
window.getMtgsig = async function(url, method, data) {
    const result = await window.MeituanRPC.generateMtgsig(url, method, data);
    if (result.success) {
        return result.mtgsig;
    } else {
        throw new Error(result.error);
    }
};

// 发送验证码专用方法
window.MeituanRPC.sendSMSCode = async function(mobile, countrycode = '86') {
    return await this.generateMtgsig(
        '/s/gateway/login/h5/login/sendLoginFreeSmsCode',
        'POST',
        { mobile, countrycode }
    );
};
```

## 使用指南

### 步骤1: 浏览器环境准备

1. 打开美团页面: https://sqt.meituan.com/
2. 打开浏览器开发者工具 (F12)
3. 切换到Console标签

### 步骤2: 运行RPC服务代码

在控制台中依次运行:

1. H5guard等待函数
2. RPC服务类定义
3. 服务初始化代码

### 步骤3: 测试签名生成

```javascript
// 测试发送验证码签名
await window.MeituanRPC.sendSMSCode('13800138000', '86');

// 测试通用签名生成
await getMtgsig('/s/gateway/user/h5/userInfo', 'GET', {});

// 运行完整测试
await window.MeituanRPC.testSignature();
```

## Python客户端集成

### 安装与使用

```bash
# 运行Python测试客户端
python real_client.py
```

### 代码示例

```python
from real_client import MeituanRPCTestClient

# 创建客户端
client = MeituanRPCTestClient()

# 生成发送验证码签名
sms_result = client.send_sms_code_signature('13800138000', '86')

# 生成通用签名
mtgsig = client.get_mtgsig_only('/api/path', 'POST', {'data': 'value'})
```

## 技术特点

### ✅ 优势

1. **真实签名生成** - 使用美团官方H5guard.sign方法
2. **完整RPC封装** - 提供标准化的调用接口
3. **错误处理完善** - 包含超时、异常等处理机制
4. **易于集成** - 支持JavaScript和Python两种调用方式
5. **实时性强** - 每次调用生成新的时间戳和签名

### ⚠️ 限制

1. **依赖浏览器环境** - 必须在美团页面中运行
2. **需要H5guard加载** - 依赖美团的安全组件
3. **跨域限制** - 受浏览器同源策略限制

## 实验结论

本实验成功验证了美团H5guard签名机制的RPC调用可行性，实现了：

1. ✅ **真实mtgsig签名生成** - 无需逆向算法，直接调用官方接口
2. ✅ **发送验证码API支持** - 专门优化的验证码发送签名
3. ✅ **通用API签名支持** - 支持任意美团API的签名生成
4. ✅ **完整的RPC服务** - 标准化的调用接口和错误处理
5. ✅ **Python客户端集成** - 便于自动化和批量处理

该方案为美团API的自动化调用提供了可靠的签名生成服务，避免了复杂的算法逆向工程。

## 文件清单

- `real_client.py` - Python测试客户端
- `docs/美团RPC签名生成服务实验报告.md` - 本实验报告
- 浏览器JavaScript代码 - 在美团页面控制台中运行

---

**实验完成时间**: 2025-01-02  
**实验状态**: ✅ 成功  
**技术验证**: ✅ 通过
